import * as React from "react"
import Layout from "../components/layout"
import { Container, Box, Heading, Text } from "../components/ui"
import SEOHead from "../components/head"

export default function TestWhatsApp() {
  return (
    <Layout>
      <Box paddingY={5}>
        <Container width="narrow">
          <Heading as="h1">Test WhatsApp FAB</Heading>
          <Text as="p">
            Cette page est utilisée pour tester le bouton WhatsApp FAB (Floating Action Button).
            Vous devriez voir un bouton WhatsApp vert flottant en bas à droite de l'écran.
          </Text>
          <Text as="p">
            Le bouton affiche le message "Besoin d'aide ?" au survol et ouvre WhatsApp 
            avec le message pré-rempli quand vous cliquez dessus.
          </Text>
          <Text as="p">
            Fonctionnalités testées :
          </Text>
          <ul>
            <li>Position fixe en bas à droite</li>
            <li>Couleur verte WhatsApp (#25D366)</li>
            <li>I<PERSON><PERSON> WhatsApp</li>
            <li><PERSON><PERSON>ip "Besoin d'aide ?" au survol</li>
            <li>Animation de scale au survol</li>
            <li>Lien vers WhatsApp avec message pré-rempli</li>
            <li>Responsive design</li>
          </ul>
        </Container>
      </Box>
    </Layout>
  )
}

export const Head = () => {
  return (
    <SEOHead 
      title="Test WhatsApp FAB"
      description="Page de test pour le bouton WhatsApp FAB"
    />
  )
}
