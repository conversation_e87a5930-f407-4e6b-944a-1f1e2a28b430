import * as React from "react"
import { graphql, useStaticQuery } from "gatsby"
import * as styles from "./ui.css"

/**
 * WhatsApp Floating Action Button (FAB) Component
 *
 * This component displays a fixed WhatsApp button in the bottom-right corner
 * of the screen with the message "Besoin d'aide ?" (Need help?).
 *
 * Features:
 * - Fixed positioning in bottom-right corner
 * - WhatsApp green color (#25D366) with hover effects
 * - Tooltip showing "Besoin d'aide ?" on hover
 * - Scale animation on hover/focus
 * - Responsive design (adjusts position on larger screens)
 * - Automatically gets WhatsApp number from CMS social links
 * - Opens WhatsApp with pre-filled message
 *
 * Configuration:
 * - Phone number can be set in DatoCMS under Layout > Footer > Social Links
 * - Add a WhatsApp social link with the phone number as username
 * - Or pass phoneNumber prop to override
 *
 * @param phoneNumber - Optional phone number override (format: +33123456789)
 * @param message - Message to pre-fill in WhatsApp (default: "Besoin d'aide ?")
 * @param tooltipText - Text to show in tooltip (default: "Besoin d'aide ?")
 */

// WhatsApp icon component (reused from footer)
const WhatsAppIcon = () => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    width="28"
    height="28"
  >
    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
  </svg>
)

interface WhatsAppFabProps {
  phoneNumber?: string
  message?: string
  tooltipText?: string
}

interface WhatsAppFabData {
  layout: {
    footer: {
      socialLinks: { id: string; service: string; username: string }[]
    }
  }
}

export default function WhatsAppFab({
  phoneNumber,
  message = "Besoin d'aide ?",
  tooltipText = "Besoin d'aide ?"
}: WhatsAppFabProps) {
  // Try to get WhatsApp number from CMS data
  const data: WhatsAppFabData = useStaticQuery(graphql`
    query {
      layout {
        footer {
          socialLinks {
            id
            service
            username
          }
        }
      }
    }
  `)

  // Find WhatsApp social link from CMS data
  const whatsappLink = data.layout.footer.socialLinks?.find(
    link => link.service === "WHATSAPP"
  )

  // Use provided phoneNumber, or CMS data, or fallback
  const finalPhoneNumber = phoneNumber || whatsappLink?.username || "+33123456789"

  const whatsappUrl = `https://wa.me/${finalPhoneNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`

  return (
    <a
      href={whatsappUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={styles.whatsappFab}
      aria-label={tooltipText}
    >
      <WhatsAppIcon />
      <span className={styles.whatsappFabTooltip}>
        {tooltipText}
      </span>
    </a>
  )
}
